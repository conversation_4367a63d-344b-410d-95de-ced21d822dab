#!/usr/bin/env python3
"""
Verification script to confirm all revision #0 entries have been updated to revision #1.
"""

import os
import sqlite3

def verify_revision_updates():
    """Verify that all revision #0 entries have been updated."""
    
    print("Revision Update Verification Report")
    print("=" * 60)
    
    # Find all database files (excluding backups)
    db_files = []
    
    for root, dirs, files in os.walk('.'):
        # Skip backup directories
        if 'backup_' in root or '/backup_' in root:
            continue
        
        for file in files:
            if file.endswith('.db'):
                db_files.append(os.path.join(root, file))
    
    # Add the main FlyrightDriveDatabase.db if it exists
    main_db_path = '/home/<USER>/Desktop/DriveDatabase/FlyrightDriveDatabase.db'
    if os.path.exists(main_db_path):
        db_files.append(main_db_path)
    
    db_files = sorted(db_files)
    
    print(f"Verifying {len(db_files)} database file(s)...\n")
    
    total_stats = {
        'databases_processed': 0,
        'databases_with_zero_revisions': 0,
        'total_zero_revision_rows': 0,
        'total_one_revision_rows': 0,
        'total_other_revision_rows': 0
    }
    
    databases_with_issues = []
    
    for db_file in db_files:
        if not os.path.exists(db_file):
            continue
        
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # Get all tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            db_has_zero_revisions = False
            db_zero_count = 0
            db_one_count = 0
            db_other_count = 0
            db_tables_with_zero = []
            
            for table_tuple in tables:
                table_name = table_tuple[0]
                
                # Check if table has a revision column
                cursor.execute(f"PRAGMA table_info({table_name});")
                columns = cursor.fetchall()
                
                revision_column = None
                for col in columns:
                    if col[1].lower() in ['revision number', 'revision_number', 'revision']:
                        revision_column = col[1]
                        break
                
                if not revision_column:
                    continue
                
                # Count rows by revision number
                cursor.execute(f'SELECT COUNT(*) FROM {table_name} WHERE "{revision_column}" = 0;')
                zero_count = cursor.fetchone()[0]
                
                cursor.execute(f'SELECT COUNT(*) FROM {table_name} WHERE "{revision_column}" = 1;')
                one_count = cursor.fetchone()[0]
                
                cursor.execute(f'SELECT COUNT(*) FROM {table_name} WHERE "{revision_column}" > 1;')
                other_count = cursor.fetchone()[0]
                
                if zero_count > 0:
                    db_has_zero_revisions = True
                    db_zero_count += zero_count
                    db_tables_with_zero.append({
                        'table': table_name,
                        'revision_column': revision_column,
                        'zero_count': zero_count
                    })
                
                db_one_count += one_count
                db_other_count += other_count
            
            total_stats['databases_processed'] += 1
            total_stats['total_one_revision_rows'] += db_one_count
            total_stats['total_other_revision_rows'] += db_other_count
            
            if db_has_zero_revisions:
                total_stats['databases_with_zero_revisions'] += 1
                total_stats['total_zero_revision_rows'] += db_zero_count
                
                databases_with_issues.append({
                    'path': db_file,
                    'zero_count': db_zero_count,
                    'tables': db_tables_with_zero
                })
            
            conn.close()
            
        except sqlite3.Error as e:
            print(f"Error processing {db_file}: {e}")
        except Exception as e:
            print(f"Unexpected error processing {db_file}: {e}")
    
    # Print results
    if databases_with_issues:
        print("⚠️  DATABASES STILL CONTAINING REVISION #0 ENTRIES:")
        print("-" * 60)
        
        for db_info in databases_with_issues:
            print(f"\n❌ {db_info['path']}")
            print(f"   Remaining revision #0 rows: {db_info['zero_count']}")
            
            for table_info in db_info['tables']:
                print(f"   📊 Table '{table_info['table']}': {table_info['zero_count']} rows")
                print(f"      Column: '{table_info['revision_column']}'")
    else:
        print("✅ SUCCESS! No revision #0 entries found in any database!")
    
    # Print summary
    print("\n" + "=" * 60)
    print("VERIFICATION SUMMARY")
    print(f"📊 Databases verified: {total_stats['databases_processed']}")
    print(f"❌ Databases still with revision #0: {total_stats['databases_with_zero_revisions']}")
    print(f"❌ Total revision #0 rows remaining: {total_stats['total_zero_revision_rows']}")
    print(f"✅ Total revision #1 rows: {total_stats['total_one_revision_rows']}")
    print(f"📈 Total higher revision rows: {total_stats['total_other_revision_rows']}")
    
    if total_stats['total_zero_revision_rows'] == 0:
        print("\n🎉 VERIFICATION PASSED! All revision #0 entries have been successfully updated to revision #1!")
    else:
        print(f"\n⚠️  VERIFICATION FAILED! {total_stats['total_zero_revision_rows']} revision #0 entries still remain.")
        print("You may need to run the update script again.")

if __name__ == "__main__":
    verify_revision_updates()
