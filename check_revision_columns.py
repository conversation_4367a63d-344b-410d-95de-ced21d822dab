#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to check all revision column names and their case variations across databases.
This will help identify case sensitivity issues.
"""

import os
import sqlite3

def check_revision_columns():
    """Check all revision column names and their variations."""
    
    print("Revision Column Analysis Report")
    print("=" * 60)
    
    # Find all database files (excluding backups)
    db_files = []
    
    for root, dirs, files in os.walk('.'):
        # Skip backup directories
        if 'backup_' in root or '/backup_' in root:
            continue
        
        for file in files:
            if file.endswith('.db'):
                db_files.append(os.path.join(root, file))
    
    # Add the main FlyrightDriveDatabase.db if it exists
    main_db_path = '/home/<USER>/Desktop/DriveDatabase/FlyrightDriveDatabase.db'
    if os.path.exists(main_db_path):
        db_files.append(main_db_path)
    
    db_files = sorted(db_files)
    
    print(f"Analyzing column names in {len(db_files)} database file(s)...\n")
    
    column_variations = {}
    databases_with_revision_columns = []
    databases_without_revision_columns = []
    
    for db_file in db_files:
        if not os.path.exists(db_file):
            continue
        
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # Get all tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            db_has_revision_column = False
            db_revision_info = []
            
            for table_tuple in tables:
                table_name = table_tuple[0]
                
                # Get all column info
                cursor.execute(f"PRAGMA table_info({table_name});")
                columns = cursor.fetchall()
                
                # Look for any column that might be a revision column
                revision_columns = []
                for col in columns:
                    col_name = col[1]
                    col_name_lower = col_name.lower()
                    
                    # Check for various revision column patterns
                    if any(pattern in col_name_lower for pattern in ['revision', 'rev']):
                        revision_columns.append(col_name)
                        
                        # Track column name variations
                        if col_name not in column_variations:
                            column_variations[col_name] = []
                        column_variations[col_name].append(f"{db_file}:{table_name}")
                
                if revision_columns:
                    db_has_revision_column = True
                    
                    # Get sample data for each revision column
                    for rev_col in revision_columns:
                        try:
                            cursor.execute(f'SELECT "{rev_col}", COUNT(*) FROM {table_name} GROUP BY "{rev_col}" ORDER BY "{rev_col}";')
                            revision_counts = cursor.fetchall()
                            
                            db_revision_info.append({
                                'table': table_name,
                                'column': rev_col,
                                'revision_counts': revision_counts
                            })
                        except sqlite3.Error as e:
                            db_revision_info.append({
                                'table': table_name,
                                'column': rev_col,
                                'error': str(e)
                            })
            
            if db_has_revision_column:
                databases_with_revision_columns.append({
                    'path': db_file,
                    'revision_info': db_revision_info
                })
            else:
                databases_without_revision_columns.append(db_file)
            
            conn.close()
            
        except sqlite3.Error as e:
            print(f"Error processing {db_file}: {e}")
        except Exception as e:
            print(f"Unexpected error processing {db_file}: {e}")
    
    # Print column name variations
    print("REVISION COLUMN NAME VARIATIONS FOUND:")
    print("-" * 60)
    for col_name, locations in column_variations.items():
        print(f"\n📊 Column Name: '{col_name}'")
        print(f"   Found in {len(locations)} location(s):")
        for location in locations[:5]:  # Show first 5 locations
            print(f"     {location}")
        if len(locations) > 5:
            print(f"     ... and {len(locations) - 5} more")
    
    # Print detailed database analysis
    print(f"\n\nDETAILED DATABASE ANALYSIS:")
    print("-" * 60)
    
    for db_info in databases_with_revision_columns:
        print(f"\n📁 {db_info['path']}")
        
        for rev_info in db_info['revision_info']:
            if 'error' in rev_info:
                print(f"   ❌ Table '{rev_info['table']}', Column '{rev_info['column']}': {rev_info['error']}")
            else:
                print(f"   📊 Table '{rev_info['table']}', Column '{rev_info['column']}':")
                for rev_value, count in rev_info['revision_counts']:
                    if rev_value == 0:
                        print(f"      🔴 Revision {rev_value}: {count} rows")
                    elif rev_value == 1:
                        print(f"      🟢 Revision {rev_value}: {count} rows")
                    else:
                        print(f"      🔵 Revision {rev_value}: {count} rows")
    
    # Print summary
    print(f"\n" + "=" * 60)
    print("SUMMARY")
    print(f"📊 Total databases analyzed: {len(db_files)}")
    print(f"✅ Databases with revision columns: {len(databases_with_revision_columns)}")
    print(f"❌ Databases without revision columns: {len(databases_without_revision_columns)}")
    print(f"📝 Unique column name variations: {len(column_variations)}")
    
    if databases_without_revision_columns:
        print(f"\nDatabases without revision columns:")
        for db_file in databases_without_revision_columns:
            print(f"  {db_file}")
    
    print(f"\nColumn name variations found:")
    for col_name in sorted(column_variations.keys()):
        print(f"  '{col_name}' (in {len(column_variations[col_name])} locations)")

if __name__ == "__main__":
    check_revision_columns()
