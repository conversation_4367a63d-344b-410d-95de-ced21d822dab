#!/usr/bin/env python3
"""
Test script to verify the Load Name population functionality without making changes.

This script will:
1. Scan backup archives and show what mappings would be created
2. Analyze databases and show what updates would be made
3. Provide a dry-run report without actually updating the databases
"""

import os
import sqlite3
import sys
import re
from pathlib import Path


def scan_backup_archives():
    """Scan backup archive directories and build mapping of revision numbers to image names."""
    backup_base_dir = "/media/flyrightbackup"
    
    if not os.path.exists(backup_base_dir):
        print(f"Warning: Backup directory {backup_base_dir} does not exist")
        return {}
    
    # Dictionary to store mappings: {(simulator, computer, revision): image_name}
    revision_to_image = {}
    
    print(f"Scanning backup archives in {backup_base_dir}...")
    
    try:
        archive_dirs = [d for d in os.listdir(backup_base_dir) 
                       if os.path.isdir(os.path.join(backup_base_dir, d))]
    except PermissionError:
        print(f"Error: Permission denied accessing {backup_base_dir}")
        return {}
    
    for archive_dir in archive_dirs:
        archive_path = os.path.join(backup_base_dir, archive_dir)
        print(f"  Archive: {archive_dir}")
        
        try:
            image_dirs = [d for d in os.listdir(archive_path) 
                         if os.path.isdir(os.path.join(archive_path, d))]
        except PermissionError:
            print(f"    Warning: Permission denied accessing {archive_path}")
            continue
        
        for image_dir in image_dirs:
            # Parse image directory name with multiple patterns:
            # Pattern 1: {simulator}-{computer}-{date}-{revision}-img (e.g., 1018-Host-09_22_2023-1-img)
            # Pattern 2: {simulator_with_underscore}-{computer}-{date}-{revision}-img (e.g., 1678_1697-Host_Software-09_21_2023-1-img)
            # Pattern 3: {simulator_with_underscore}-{computer}-{date}-{revision}-img (e.g., 393_423-2ULDT1-10_14_2023-1-img)

            match = None
            simulator = None
            computer = None
            date = None
            revision = None

            # Try Pattern 1: Simple simulator number
            match = re.match(r'^(\d+)-([^-]+)-([^-]+)-(\d+)-img$', image_dir)
            if match:
                simulator, computer, date, revision = match.groups()

            # Try Pattern 2: Simulator with underscore (like 1678_1697 or 393_423)
            if not match:
                match = re.match(r'^(\d+_\d+)-([^-]+)-([^-]+)-(\d+)-img$', image_dir)
                if match:
                    simulator, computer, date, revision = match.groups()

            if match:
                key = (simulator, computer, revision)
                revision_to_image[key] = image_dir
                print(f"    ✓ {image_dir}")
                print(f"      -> Simulator: {simulator}, Computer: {computer}, Revision: {revision}")
            else:
                print(f"    ✗ Skipping: {image_dir} (doesn't match pattern)")
    
    return revision_to_image


def get_all_db_files(root_dir):
    """Recursively find all .db files in the directory tree, excluding backup directories."""
    db_files = []
    for root, dirs, files in os.walk(root_dir):
        # Skip backup directories
        dirs[:] = [d for d in dirs if not d.startswith('backup_')]
        
        for file in files:
            if file.endswith('.db'):
                db_files.append(os.path.join(root, file))
    return sorted(db_files)


def get_table_names(db_path):
    """Get all table names from a SQLite database."""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]
        conn.close()
        return tables
    except Exception as e:
        print(f"Error getting tables from {db_path}: {e}")
        return []


def column_exists(db_path, table_name, column_name):
    """Check if a column exists in a table."""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = [row[1] for row in cursor.fetchall()]
        conn.close()
        return column_name in columns
    except Exception as e:
        print(f"Error checking column in {db_path}.{table_name}: {e}")
        return False


def analyze_database(db_path, revision_to_image):
    """Analyze what updates would be made to a database (dry run)."""
    # Extract simulator and computer from database path
    path_parts = db_path.replace('\\', '/').split('/')
    if len(path_parts) < 2:
        print(f"  Warning: Cannot extract simulator/computer from path: {db_path}")
        return 0
    
    simulator = path_parts[-2]  # Directory name (e.g., "1018")
    computer = path_parts[-1].replace('.db', '')  # Filename without .db (e.g., "Host")
    
    print(f"  Database: {db_path}")
    print(f"    Simulator: {simulator}, Computer: {computer}")
    
    tables = get_table_names(db_path)
    if not tables:
        print(f"    No tables found")
        return 0
    
    total_potential_updates = 0
    
    for table in tables:
        if not column_exists(db_path, table, "Load Name"):
            print(f"    Table '{table}': Missing 'Load Name' column - would skip")
            continue
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Get all rows with revision numbers
            cursor.execute(f'SELECT rowid, "Revision Number", "Load Name" FROM "{table}" WHERE "Revision Number" IS NOT NULL AND "Revision Number" != ""')
            rows = cursor.fetchall()
            
            potential_updates = 0
            for rowid, revision, current_load_name in rows:
                if revision and revision.strip():
                    key = (simulator, computer, revision.strip())
                    if key in revision_to_image:
                        image_name = revision_to_image[key]
                        if current_load_name != image_name:
                            potential_updates += 1
                            print(f"      Row {rowid}: Revision {revision}")
                            print(f"        Current Load Name: '{current_load_name or '(empty)'}'")
                            print(f"        Would update to: '{image_name}'")
                    else:
                        print(f"      Row {rowid}: Revision {revision} - No matching backup image found")
            
            conn.close()
            
            if potential_updates > 0:
                print(f"    Table '{table}': {potential_updates} rows would be updated")
                total_potential_updates += potential_updates
            else:
                print(f"    Table '{table}': No updates needed")
                
        except Exception as e:
            print(f"    Error analyzing table '{table}': {e}")
    
    return total_potential_updates


def main():
    """Main function for dry run analysis."""
    print("Load Name Population Analysis (Dry Run)")
    print("=" * 45)
    
    # Scan backup archives first
    print("STEP 1: Scanning backup archives")
    print("-" * 30)
    revision_to_image = scan_backup_archives()
    
    print(f"\nFound {len(revision_to_image)} backup images:")
    for (simulator, computer, revision), image_name in sorted(revision_to_image.items()):
        print(f"  {simulator}-{computer}-{revision} -> {image_name}")
    
    if not revision_to_image:
        print("No backup images found. Exiting.")
        return
    
    # Analyze databases
    print(f"\nSTEP 2: Analyzing databases")
    print("-" * 30)
    
    root_dir = os.getcwd()
    db_files = get_all_db_files(root_dir)
    
    if not db_files:
        print("No .db files found in the directory tree.")
        return
    
    print(f"Found {len(db_files)} database file(s)")
    
    total_potential_updates = 0
    
    for db_file in db_files:
        print(f"\n{db_file}:")
        try:
            updates = analyze_database(db_file, revision_to_image)
            total_potential_updates += updates
        except Exception as e:
            print(f"  Error analyzing {db_file}: {e}")
    
    print(f"\n" + "=" * 45)
    print(f"ANALYSIS SUMMARY")
    print(f"Total potential Load Name updates: {total_potential_updates}")
    print(f"\nTo actually perform these updates, run: python3 populate_load_names.py")


if __name__ == "__main__":
    main()
