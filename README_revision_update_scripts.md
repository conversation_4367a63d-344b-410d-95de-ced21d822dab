# Revision Number Update Scripts

This directory contains scripts to update all revision #0 entries to revision #1 across all database files.

## Scripts Overview

### 1. `test_revision_update.py`
**Purpose**: Analysis script to identify all revision #0 entries before making changes.

**Usage**:
```bash
python3 test_revision_update.py
```

**Features**:
- Scans all database files (excluding backup directories)
- Identifies tables with revision columns
- Counts revision #0 entries in each database
- Provides detailed breakdown by database and table
- Safe read-only operation

### 2. `update_revision_zero_to_one.py`
**Purpose**: Main script to update all revision #0 entries to revision #1.

**Usage**:
```bash
# Dry run (preview changes without making them)
python3 update_revision_zero_to_one.py --dry-run

# Actual update (with automatic backups)
python3 update_revision_zero_to_one.py

# Update without creating backups (not recommended)
python3 update_revision_zero_to_one.py --no-backup
```

**Features**:
- Automatically creates timestamped backups before making changes
- Supports dry-run mode to preview changes
- Processes both simulator databases and main FlyrightDriveDatabase.db
- Excludes backup directories to avoid duplicate processing
- Handles multiple problematic revision value types:
  - Integer 0 values
  - Empty string values (`''`)
  - NULL values
- Comprehensive error handling and logging
- Interactive confirmation before making changes

### 3. `verify_revision_update.py`
**Purpose**: Verification script to confirm all updates were successful.

**Usage**:
```bash
python3 verify_revision_update.py
```

**Features**:
- Verifies no revision #0 entries remain
- Counts revision #1 and higher revision entries
- Provides comprehensive verification report
- Identifies any databases that still need updates

## Execution Results

### Initial Run (2025-09-25 18:41:19)

**Databases Processed**: 63 databases
- 52 databases contained revision #0 entries
- 11 databases had no revision #0 entries

**Updates Made**:
- **174 rows** updated from revision #0 to revision #1
- Updates applied to both simulator databases and main FlyrightDriveDatabase.db
- All databases automatically backed up before changes

### Case Sensitivity Fix Run (2025-09-25 18:54:06)

**Issue Discovered**: Some databases contained empty string values (`''`) instead of integer 0
**Databases with Issues**: 7 databases
**Additional Updates Made**:
- **14 rows** updated from empty string to revision #1
- Script enhanced to handle NULL, empty string, and integer 0 values
- All databases automatically backed up before changes

**Final Verification Results**:
- ✅ **0 revision #0 entries remaining**
- ✅ **0 empty string revision entries remaining**
- ✅ **0 NULL revision entries remaining**
- ✅ **217 revision #1 entries** (includes all updated + existing)
- ✅ **84 higher revision entries** (revision #2, #3, etc.)

**Total Updates**: **188 rows** updated across both runs

## Database Coverage

### Simulator Databases Updated:
- **1018**: Controls, Host, Motion, Sound
- **1329**: Audio, Avionics, Controls, Host, IOS, Motion, SDS
- **1477**: Primary_Audio_Software, Primary_Audio_Windows, Primary_Controls_Software, Primary_Controls_Windows, Secondary_Audio_Software, Secondary_Audio_Windows, Secondary_Controls_Software, Secondary_Controls_Windows
- **1678_1697**: Controls_Software, Controls_Windows, Sound_Software, Sound_Windows, WXRadar_Software, WXRadar_Windows
- **393_423**: Host, IOS_Master, IOS_Slave, TROPOS_2ULDT1-6, TROPOS_2ULDTCS, TROPOS_2ULDTDBW, TROPOS_2ULDTDBW_2TB
- **657**: Audio_Software, Audio_Windows, Controls_Software, Controls_Windows, G1000_Software, G1000_Windows, Host, IOS1_Software, IOS1_Windows, IOS1_Windows_SSD, IOS2_Software, IOS2_Windows, Motion, WXRDR

### Main Database Updated:
- **FlyrightDriveDatabase.db**: 73 rows updated in 'Drives' table

## Backup Files

All modified databases have timestamped backup files created:
- Format: `{original_filename}.backup_20250925_184119`
- Backups contain the original data before revision updates
- Can be restored if needed by renaming back to original filename

## Safety Features

1. **Automatic Backups**: Every database is backed up before modification
2. **Dry Run Mode**: Preview all changes before applying them
3. **Interactive Confirmation**: User must confirm before making changes
4. **Backup Directory Exclusion**: Prevents processing backup files
5. **Comprehensive Verification**: Post-update verification ensures success
6. **Error Handling**: Graceful handling of database access issues

## Column Detection

The scripts automatically detect revision columns with these names:
- `Revision Number` (most common)
- `revision_number`
- `revision`

## Future Use

These scripts can be run again in the future if new databases are added or if revision #0 entries are created. The scripts will:
- Only process databases that actually contain revision #0 entries
- Skip databases that have already been updated
- Maintain all existing revision numbers > 0

## Troubleshooting

If verification fails:
1. Check the verification report for specific databases with issues
2. Re-run the update script on problem databases
3. Examine backup files if restoration is needed
4. Check database permissions and file access

## Related Scripts

These revision update scripts work alongside the Load Name population scripts:
- `populate_load_names.py` - Populates Load Name columns based on backup images
- `test_populate_load_names.py` - Analyzes Load Name population opportunities
- `verify_load_name_population.py` - Verifies Load Name population results
