#!/usr/bin/env python3

import os
import re

def scan_dash_directory():
    """Debug script to see what files are found in DASH_BKUPS3"""
    
    backup_root = "/media/flyrightbackup"
    dash_dir = os.path.join(backup_root, "DASH_BKUPS3")
    
    print(f"Scanning directory: {dash_dir}")
    print(f"Directory exists: {os.path.exists(dash_dir)}")
    print(f"Directory is readable: {os.access(dash_dir, os.R_OK)}")
    
    if not os.path.exists(dash_dir):
        print("Directory does not exist!")
        return
    
    try:
        files = os.listdir(dash_dir)
        print(f"Total files found: {len(files)}")
        
        # Filter for image files
        img_files = [f for f in files if f.endswith('-img')]
        print(f"Image files found: {len(img_files)}")
        
        # Test regex patterns
        pattern1 = r'^(\d+)-([^-]+)-([^-]+)-(\d+)-img$'
        pattern2 = r'^(\d+_\d+)-([^-]+)-([^-]+)-(\d+)-img$'
        
        print("\nTesting regex patterns:")
        for file in sorted(img_files):
            match1 = re.match(pattern1, file)
            match2 = re.match(pattern2, file)
            
            if match1:
                simulator, computer, date, revision = match1.groups()
                print(f"✓ Pattern1: {file} -> {simulator}, {computer}, {revision}")
            elif match2:
                simulator, computer, date, revision = match2.groups()
                print(f"✓ Pattern2: {file} -> {simulator}, {computer}, {revision}")
            else:
                print(f"✗ No match: {file}")
                
        # Specifically look for Host and IOS_Master
        print("\nHost and IOS_Master files:")
        host_files = [f for f in img_files if 'Host' in f]
        ios_files = [f for f in img_files if 'IOS_Master' in f]
        
        print(f"Host files: {len(host_files)}")
        for f in host_files:
            print(f"  {f}")
            
        print(f"IOS_Master files: {len(ios_files)}")
        for f in ios_files:
            print(f"  {f}")
            
    except Exception as e:
        print(f"Error scanning directory: {e}")

if __name__ == "__main__":
    scan_dash_directory()
