#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to update all revision #0 entries to revision #1 in database files.

This script will:
1. Update all simulator databases in the current directory and subdirectories
2. Update the main FlyrightDriveDatabase.db file
3. Provide detailed logging of all changes made
4. Create backups before making changes
5. Allow dry-run mode to preview changes

Usage:
    python3 update_revision_zero_to_one.py [--dry-run] [--no-backup]
"""

import os
import sqlite3
import shutil
import argparse
from datetime import datetime
import glob

def create_backup(db_path):
    """Create a backup of the database file."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{db_path}.backup_{timestamp}"
    shutil.copy2(db_path, backup_path)
    print(f"  Created backup: {backup_path}")
    return backup_path

def update_revision_zero_to_one(db_path, dry_run=False, create_backup_flag=True):
    """
    Update all revision #0 entries to revision #1 in a database file.
    
    Args:
        db_path: Path to the database file
        dry_run: If True, only show what would be changed without making changes
        create_backup_flag: If True, create a backup before making changes
    
    Returns:
        Dictionary with update statistics
    """
    stats = {
        'total_rows_found': 0,
        'total_rows_updated': 0,
        'tables_processed': [],
        'backup_created': None
    }
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables in the database
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        if not tables:
            print(f"  No tables found in database")
            conn.close()
            return stats
        
        # Create backup if not in dry-run mode and backup is requested
        if not dry_run and create_backup_flag:
            stats['backup_created'] = create_backup(db_path)
        
        for table_tuple in tables:
            table_name = table_tuple[0]
            
            # Check if table has a "Revision Number" column
            cursor.execute(f"PRAGMA table_info({table_name});")
            columns = cursor.fetchall()
            
            revision_column = None
            for col in columns:
                if col[1].lower() in ['revision number', 'revision_number', 'revision']:
                    revision_column = col[1]
                    break
            
            if not revision_column:
                continue
            
            # Find rows with revision #0
            cursor.execute(f'SELECT rowid, "{revision_column}" FROM {table_name} WHERE "{revision_column}" = 0;')
            rows_with_zero = cursor.fetchall()
            
            if rows_with_zero:
                stats['total_rows_found'] += len(rows_with_zero)
                stats['tables_processed'].append({
                    'table': table_name,
                    'revision_column': revision_column,
                    'rows_found': len(rows_with_zero),
                    'rows_updated': 0
                })
                
                print(f"  Table '{table_name}': Found {len(rows_with_zero)} rows with revision #0")
                
                if dry_run:
                    for row in rows_with_zero:
                        print(f"    Would update row {row[0]}: Revision {row[1]} -> 1")
                else:
                    # Update rows from revision 0 to revision 1
                    cursor.execute(f'UPDATE {table_name} SET "{revision_column}" = 1 WHERE "{revision_column}" = 0;')
                    rows_updated = cursor.rowcount
                    stats['total_rows_updated'] += rows_updated
                    stats['tables_processed'][-1]['rows_updated'] = rows_updated
                    
                    print(f"    Updated {rows_updated} rows: Revision 0 -> 1")
        
        if not dry_run:
            conn.commit()
        conn.close()
        
    except sqlite3.Error as e:
        print(f"  Error processing database: {e}")
    except Exception as e:
        print(f"  Unexpected error: {e}")
    
    return stats

def find_database_files():
    """Find all database files to process."""
    db_files = []

    # Find all .db files in current directory and subdirectories
    # Exclude backup directories
    for root, dirs, files in os.walk('.'):
        # Skip backup directories
        if 'backup_' in root or '/backup_' in root:
            continue

        for file in files:
            if file.endswith('.db'):
                db_files.append(os.path.join(root, file))

    # Add the main FlyrightDriveDatabase.db if it exists
    main_db_path = '/home/<USER>/Desktop/DriveDatabase/FlyrightDriveDatabase.db'
    if os.path.exists(main_db_path):
        db_files.append(main_db_path)

    return sorted(db_files)

def main():
    parser = argparse.ArgumentParser(description='Update revision #0 to revision #1 in database files')
    parser.add_argument('--dry-run', action='store_true', 
                       help='Show what would be changed without making actual changes')
    parser.add_argument('--no-backup', action='store_true',
                       help='Skip creating backup files (not recommended)')
    
    args = parser.parse_args()
    
    print("Revision Number Update Script")
    print("=" * 50)
    
    if args.dry_run:
        print("DRY RUN MODE - No changes will be made")
        print("=" * 50)
    
    # Find all database files
    db_files = find_database_files()
    
    if not db_files:
        print("No database files found!")
        return
    
    print(f"Found {len(db_files)} database file(s) to process:")
    for db_file in db_files:
        print(f"  {db_file}")
    print()
    
    # Ask for confirmation unless in dry-run mode
    if not args.dry_run:
        response = input("Proceed with updating revision numbers? (y/N): ")
        if response.lower() != 'y':
            print("Operation cancelled.")
            return
        print()
    
    # Process each database
    total_stats = {
        'databases_processed': 0,
        'total_rows_found': 0,
        'total_rows_updated': 0,
        'databases_with_changes': 0
    }
    
    for db_file in db_files:
        print(f"Processing: {db_file}")
        
        if not os.path.exists(db_file):
            print(f"  Warning: File does not exist, skipping")
            continue
        
        stats = update_revision_zero_to_one(
            db_file, 
            dry_run=args.dry_run, 
            create_backup_flag=not args.no_backup
        )
        
        total_stats['databases_processed'] += 1
        total_stats['total_rows_found'] += stats['total_rows_found']
        total_stats['total_rows_updated'] += stats['total_rows_updated']
        
        if stats['total_rows_found'] > 0:
            total_stats['databases_with_changes'] += 1
        
        if stats['total_rows_found'] == 0:
            print(f"  No revision #0 entries found")
        
        print()
    
    # Print summary
    print("=" * 50)
    print("SUMMARY")
    print(f"Databases processed: {total_stats['databases_processed']}")
    print(f"Databases with revision #0 entries: {total_stats['databases_with_changes']}")
    print(f"Total rows with revision #0 found: {total_stats['total_rows_found']}")
    
    if args.dry_run:
        print(f"Total rows that would be updated: {total_stats['total_rows_found']}")
        print("\nTo actually make these changes, run the script without --dry-run")
    else:
        print(f"Total rows updated: {total_stats['total_rows_updated']}")
        if not args.no_backup:
            print("\nBackup files were created for all modified databases")

if __name__ == "__main__":
    main()
