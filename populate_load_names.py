#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to populate the Load Name column based on revision numbers and corresponding backup image names.

This script will:
1. Scan all backup archive directories in /media/flyrightbackup/
2. Build a mapping of revision numbers to backup image names
3. For each database, update the Load Name column based on revision numbers
4. Handle the backup image naming convention: {simulator}-{computer}-{date}-{revision_number}-img
"""

import os
import sqlite3
import sys
import re
from pathlib import Path
from collections import defaultdict


def get_all_db_files(root_dir):
    """Recursively find all .db files in the directory tree, excluding backup directories."""
    db_files = []
    for root, dirs, files in os.walk(root_dir):
        # Skip backup directories
        dirs[:] = [d for d in dirs if not d.startswith('backup_')]
        
        for file in files:
            if file.endswith('.db'):
                db_files.append(os.path.join(root, file))
    return sorted(db_files)


def scan_backup_archives():
    """Scan backup archive directories and build mapping of revision numbers to image names."""
    media_dir = "/media"

    if not os.path.exists(media_dir):
        print(f"Warning: Media directory {media_dir} does not exist")
        return {}

    # Dictionary to store mappings: {(simulator, computer, revision): image_name}
    revision_to_image = {}

    # Find all flyright* directories in /media/
    flyright_dirs = []
    try:
        for item in os.listdir(media_dir):
            item_path = os.path.join(media_dir, item)
            if os.path.isdir(item_path) and item.startswith('flyright'):
                flyright_dirs.append(item_path)
    except PermissionError:
        print(f"Error: Permission denied accessing {media_dir}")
        return {}

    if not flyright_dirs:
        print(f"Warning: No flyright* directories found in {media_dir}")
        return {}

    print(f"Found flyright directories: {[os.path.basename(d) for d in flyright_dirs]}")

    for backup_base_dir in flyright_dirs:
        print(f"\nScanning backup archives in {backup_base_dir}...")

        try:
            archive_dirs = [d for d in os.listdir(backup_base_dir)
                           if os.path.isdir(os.path.join(backup_base_dir, d))]
        except PermissionError:
            print(f"  Error: Permission denied accessing {backup_base_dir}")
            continue

        for archive_dir in archive_dirs:
            archive_path = os.path.join(backup_base_dir, archive_dir)
            print(f"  Scanning archive: {archive_dir}")

            try:
                # Get both directories and files that end with '-img'
                all_items = os.listdir(archive_path)
                image_items = []
                for item in all_items:
                    item_path = os.path.join(archive_path, item)
                    # Include directories or files that end with '-img'
                    if (os.path.isdir(item_path) or item.endswith('-img')):
                        image_items.append(item)
            except PermissionError:
                print(f"    Warning: Permission denied accessing {archive_path}")
                continue

            for image_item in image_items:
                # Parse image item name (directory or file) with multiple patterns:
                # Pattern 1: {simulator}-{computer}-{date}-{revision}-img (e.g., 1018-Host-09_22_2023-1-img)
                # Pattern 2: {simulator_with_underscore}-{computer}-{date}-{revision}-img (e.g., 1678_1697-Host_Software-09_21_2023-1-img)
                # Pattern 3: {simulator_with_underscore}-{computer}-{date}-{revision}-img (e.g., 393_423-2ULDT1-10_14_2023-1-img)

                match = None
                simulator = None
                computer = None
                date = None
                revision = None

                # Try Pattern 1: Simple simulator number
                match = re.match(r'^(\d+)-([^-]+)-([^-]+)-(\d+)-img$', image_item)
                if match:
                    simulator, computer, date, revision = match.groups()

                # Try Pattern 2: Simulator with underscore (like 1678_1697 or 393_423)
                if not match:
                    match = re.match(r'^(\d+_\d+)-([^-]+)-([^-]+)-(\d+)-img$', image_item)
                    if match:
                        simulator, computer, date, revision = match.groups()

                if match:
                    # Store both exact case and lowercase versions for matching
                    key_exact = (simulator, computer, revision)
                    key_lower = (simulator, computer.lower(), revision)

                    # Store the full path to the image item for reference
                    revision_to_image[key_exact] = image_item
                    # Also store lowercase version for case-insensitive matching
                    revision_to_image[key_lower] = image_item
                    print(f"    Found: {image_item} -> Simulator: {simulator}, Computer: {computer}, Revision: {revision}")
                else:
                    print(f"    Skipping non-matching item: {image_item}")

    print(f"\nFound {len(revision_to_image)} backup images with revision numbers across all flyright directories")
    return revision_to_image


def get_table_names(db_path):
    """Get all table names from a SQLite database."""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]
        conn.close()
        return tables
    except Exception as e:
        print(f"Error getting tables from {db_path}: {e}")
        return []


def column_exists(db_path, table_name, column_name):
    """Check if a column exists in a table."""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = [row[1] for row in cursor.fetchall()]
        conn.close()
        return column_name in columns
    except Exception as e:
        print(f"Error checking column in {db_path}.{table_name}: {e}")
        return False


def update_load_names(db_path, revision_to_image):
    """Update Load Name column in all tables of a database."""
    # Extract simulator and computer from database path
    # Path format: ./1018/Host.db -> simulator=1018, computer=Host
    path_parts = db_path.replace('\\', '/').split('/')
    if len(path_parts) < 2:
        print(f"  Warning: Cannot extract simulator/computer from path: {db_path}")
        return 0
    
    simulator = path_parts[-2]  # Directory name (e.g., "1018")
    computer = path_parts[-1].replace('.db', '')  # Filename without .db (e.g., "Host")
    
    print(f"  Processing database: Simulator={simulator}, Computer={computer}")
    
    tables = get_table_names(db_path)
    if not tables:
        print(f"    No tables found")
        return 0
    
    total_updates = 0
    
    for table in tables:
        if not column_exists(db_path, table, "Load Name"):
            print(f"    Table '{table}': 'Load Name' column does not exist - skipping")
            continue
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Get all rows with revision numbers
            cursor.execute(f'SELECT rowid, "Revision Number" FROM "{table}" WHERE "Revision Number" IS NOT NULL AND "Revision Number" != ""')
            rows = cursor.fetchall()
            
            updates_in_table = 0
            for rowid, revision in rows:
                if revision and revision.strip():
                    # Look for matching backup image
                    # Try exact case match first
                    key_exact = (simulator, computer, revision.strip())
                    # Try case-insensitive match as fallback
                    key_lower = (simulator, computer.lower(), revision.strip())

                    image_name = None
                    if key_exact in revision_to_image:
                        image_name = revision_to_image[key_exact]
                    elif key_lower in revision_to_image:
                        image_name = revision_to_image[key_lower]

                    if image_name:
                        cursor.execute(f'UPDATE "{table}" SET "Load Name" = ? WHERE rowid = ?',
                                     (image_name, rowid))
                        updates_in_table += 1
                        print(f"      Updated row {rowid}: Revision {revision} -> {image_name}")
            
            conn.commit()
            conn.close()
            
            if updates_in_table > 0:
                print(f"    Table '{table}': Updated {updates_in_table} rows")
                total_updates += updates_in_table
            else:
                print(f"    Table '{table}': No matching backup images found")
                
        except Exception as e:
            print(f"    Error updating table '{table}': {e}")
    
    return total_updates


def main():
    """Main function."""
    print("Load Name Population Script")
    print("=" * 40)
    
    # Scan backup archives first
    revision_to_image = scan_backup_archives()
    
    if not revision_to_image:
        print("No backup images found. Exiting.")
        return
    
    # Get current directory and find databases
    root_dir = os.getcwd()
    print(f"\nScanning databases in: {root_dir}")
    
    db_files = get_all_db_files(root_dir)
    
    if not db_files:
        print("No .db files found in the directory tree.")
        return
    
    print(f"Found {len(db_files)} database file(s)")
    
    # Ask for confirmation
    response = input(f"\nProceed with updating Load Name columns? (y/N): ")
    if response.lower() not in ['y', 'yes']:
        print("Operation cancelled.")
        return
    
    # Process each database
    total_updates = 0
    processed_databases = 0
    
    for db_file in db_files:
        print(f"\nProcessing: {db_file}")
        try:
            updates = update_load_names(db_file, revision_to_image)
            total_updates += updates
            processed_databases += 1
        except Exception as e:
            print(f"Error processing {db_file}: {e}")
    
    print(f"\n" + "=" * 40)
    print(f"Processing complete!")
    print(f"Processed {processed_databases} database(s)")
    print(f"Total Load Name updates: {total_updates}")


if __name__ == "__main__":
    main()
