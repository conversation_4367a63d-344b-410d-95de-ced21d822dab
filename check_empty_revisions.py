#!/usr/bin/env python3
"""
Scrip<PERSON> to check for NULL, empty string, and other non-zero revision values.
"""

import os
import sqlite3

def check_empty_revisions():
    """Check for NULL, empty, and other problematic revision values."""
    
    print("Empty/NULL Revision Analysis Report")
    print("=" * 60)
    
    # Find all database files (excluding backups)
    db_files = []
    
    for root, dirs, files in os.walk('.'):
        # Skip backup directories
        if 'backup_' in root or '/backup_' in root:
            continue
        
        for file in files:
            if file.endswith('.db'):
                db_files.append(os.path.join(root, file))
    
    # Add the main FlyrightDriveDatabase.db if it exists
    main_db_path = '/home/<USER>/Desktop/DriveDatabase/FlyrightDriveDatabase.db'
    if os.path.exists(main_db_path):
        db_files.append(main_db_path)
    
    db_files = sorted(db_files)
    
    print(f"Analyzing {len(db_files)} database file(s) for empty/NULL revisions...\n")
    
    databases_with_issues = []
    total_problematic_rows = 0
    
    for db_file in db_files:
        if not os.path.exists(db_file):
            continue
        
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # Get all tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            db_issues = []
            
            for table_tuple in tables:
                table_name = table_tuple[0]
                
                # Check if table has a revision column
                cursor.execute(f"PRAGMA table_info({table_name});")
                columns = cursor.fetchall()
                
                revision_column = None
                for col in columns:
                    if col[1].lower() in ['revision number', 'revision_number', 'revision']:
                        revision_column = col[1]
                        break
                
                if not revision_column:
                    continue
                
                # Check for various problematic values
                issues = []
                
                # Check for NULL values
                cursor.execute(f'SELECT rowid, "{revision_column}" FROM {table_name} WHERE "{revision_column}" IS NULL;')
                null_rows = cursor.fetchall()
                if null_rows:
                    issues.append({
                        'type': 'NULL',
                        'count': len(null_rows),
                        'rows': null_rows[:5]  # Show first 5 rows
                    })
                
                # Check for empty string values
                cursor.execute(f'SELECT rowid, "{revision_column}" FROM {table_name} WHERE "{revision_column}" = "";')
                empty_rows = cursor.fetchall()
                if empty_rows:
                    issues.append({
                        'type': 'EMPTY_STRING',
                        'count': len(empty_rows),
                        'rows': empty_rows[:5]
                    })
                
                # Check for zero values (integer 0)
                cursor.execute(f'SELECT rowid, "{revision_column}" FROM {table_name} WHERE "{revision_column}" = 0;')
                zero_rows = cursor.fetchall()
                if zero_rows:
                    issues.append({
                        'type': 'ZERO',
                        'count': len(zero_rows),
                        'rows': zero_rows[:5]
                    })
                
                # Check for string "0"
                cursor.execute(f'SELECT rowid, "{revision_column}" FROM {table_name} WHERE "{revision_column}" = "0";')
                string_zero_rows = cursor.fetchall()
                if string_zero_rows:
                    issues.append({
                        'type': 'STRING_ZERO',
                        'count': len(string_zero_rows),
                        'rows': string_zero_rows[:5]
                    })
                
                # Check for any other non-numeric values that aren't NULL or empty
                cursor.execute(f'''
                    SELECT rowid, "{revision_column}" 
                    FROM {table_name} 
                    WHERE "{revision_column}" IS NOT NULL 
                    AND "{revision_column}" != "" 
                    AND CAST("{revision_column}" AS INTEGER) != "{revision_column}"
                    AND "{revision_column}" NOT LIKE "%[0-9]%"
                ''')
                non_numeric_rows = cursor.fetchall()
                if non_numeric_rows:
                    issues.append({
                        'type': 'NON_NUMERIC',
                        'count': len(non_numeric_rows),
                        'rows': non_numeric_rows[:5]
                    })
                
                if issues:
                    db_issues.append({
                        'table': table_name,
                        'column': revision_column,
                        'issues': issues
                    })
                    
                    # Count total problematic rows
                    for issue in issues:
                        total_problematic_rows += issue['count']
            
            if db_issues:
                databases_with_issues.append({
                    'path': db_file,
                    'issues': db_issues
                })
            
            conn.close()
            
        except sqlite3.Error as e:
            print(f"Error processing {db_file}: {e}")
        except Exception as e:
            print(f"Unexpected error processing {db_file}: {e}")
    
    # Print detailed results
    if databases_with_issues:
        print("DATABASES WITH PROBLEMATIC REVISION VALUES:")
        print("-" * 60)
        
        for db_info in databases_with_issues:
            print(f"\n📁 {db_info['path']}")
            
            for table_info in db_info['issues']:
                print(f"   📊 Table '{table_info['table']}', Column '{table_info['column']}':")
                
                for issue in table_info['issues']:
                    print(f"      ❌ {issue['type']}: {issue['count']} rows")
                    for row in issue['rows']:
                        print(f"         Row {row[0]}: '{row[1]}'")
                    if issue['count'] > 5:
                        print(f"         ... and {issue['count'] - 5} more")
    else:
        print("✅ No problematic revision values found!")
    
    # Print summary
    print(f"\n" + "=" * 60)
    print("SUMMARY")
    print(f"📊 Databases analyzed: {len(db_files)}")
    print(f"❌ Databases with issues: {len(databases_with_issues)}")
    print(f"❌ Total problematic rows: {total_problematic_rows}")
    
    if total_problematic_rows > 0:
        print(f"\n🔧 These values need to be updated to revision #1")
        print("The update script needs to be modified to handle these cases.")

if __name__ == "__main__":
    check_empty_revisions()
