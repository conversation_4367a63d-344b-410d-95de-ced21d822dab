#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create backups of all SQLite database files before modification.

This script will:
1. Recursively find all .db files
2. Create a backup directory with timestamp
3. Copy all .db files to the backup directory maintaining directory structure
"""

import os
import shutil
import sys
from datetime import datetime
from pathlib import Path


def get_all_db_files(root_dir):
    """Recursively find all .db files in the directory tree."""
    db_files = []
    for root, dirs, files in os.walk(root_dir):
        for file in files:
            if file.endswith('.db'):
                db_files.append(os.path.join(root, file))
    return sorted(db_files)


def create_backup_directory():
    """Create a timestamped backup directory."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backup_{timestamp}"
    os.makedirs(backup_dir, exist_ok=True)
    return backup_dir


def backup_databases(root_dir):
    """Create backups of all database files."""
    print("Database Backup Script")
    print("=" * 30)
    
    # Find all .db files
    db_files = get_all_db_files(root_dir)
    
    if not db_files:
        print("No .db files found in the directory tree.")
        return
    
    print(f"Found {len(db_files)} database file(s) to backup:")
    for db_file in db_files:
        print(f"  {db_file}")
    
    # Create backup directory
    backup_dir = create_backup_directory()
    print(f"\nCreating backup directory: {backup_dir}")
    
    # Copy each file maintaining directory structure
    success_count = 0
    for db_file in db_files:
        try:
            # Calculate relative path from root
            rel_path = os.path.relpath(db_file, root_dir)
            backup_path = os.path.join(backup_dir, rel_path)
            
            # Create subdirectories if needed
            backup_subdir = os.path.dirname(backup_path)
            if backup_subdir:
                os.makedirs(backup_subdir, exist_ok=True)
            
            # Copy the file
            shutil.copy2(db_file, backup_path)
            print(f"  Backed up: {rel_path}")
            success_count += 1
            
        except Exception as e:
            print(f"  ERROR backing up {db_file}: {e}")
    
    print(f"\nBackup complete!")
    print(f"Successfully backed up {success_count}/{len(db_files)} files")
    print(f"Backup location: {os.path.abspath(backup_dir)}")


def main():
    """Main function."""
    root_dir = os.getcwd()
    print(f"Scanning directory: {root_dir}")
    backup_databases(root_dir)


if __name__ == "__main__":
    main()
