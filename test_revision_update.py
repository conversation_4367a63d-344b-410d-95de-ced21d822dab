#!/usr/bin/env python3
"""
Test script to analyze revision #0 entries before updating them.

This script provides detailed analysis of all revision #0 entries
across all database files without making any changes.
"""

import os
import sqlite3
import glob

def analyze_revision_zero_entries():
    """Analyze all revision #0 entries in database files."""
    
    print("Revision #0 Analysis Report")
    print("=" * 60)
    
    # Find all database files
    db_files = []
    
    # Find all .db files in current directory and subdirectories
    # Exclude backup directories
    for root, dirs, files in os.walk('.'):
        # Skip backup directories
        if 'backup_' in root or '/backup_' in root:
            continue

        for file in files:
            if file.endswith('.db'):
                db_files.append(os.path.join(root, file))
    
    # Add the main FlyrightDriveDatabase.db if it exists
    main_db_path = '/home/<USER>/Desktop/DriveDatabase/FlyrightDriveDatabase.db'
    if os.path.exists(main_db_path):
        db_files.append(main_db_path)
    
    db_files = sorted(db_files)
    
    if not db_files:
        print("No database files found!")
        return
    
    print(f"Analyzing {len(db_files)} database file(s)...\n")
    
    total_stats = {
        'databases_processed': 0,
        'databases_with_zero_revisions': 0,
        'total_zero_revision_rows': 0,
        'tables_with_zero_revisions': 0
    }
    
    databases_with_changes = []
    
    for db_file in db_files:
        if not os.path.exists(db_file):
            continue
        
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # Get all tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            db_has_zero_revisions = False
            db_zero_count = 0
            db_tables_with_zero = []
            
            for table_tuple in tables:
                table_name = table_tuple[0]
                
                # Check if table has a revision column
                cursor.execute(f"PRAGMA table_info({table_name});")
                columns = cursor.fetchall()
                
                revision_column = None
                for col in columns:
                    if col[1].lower() in ['revision number', 'revision_number', 'revision']:
                        revision_column = col[1]
                        break
                
                if not revision_column:
                    continue
                
                # Count rows with revision #0
                cursor.execute(f'SELECT COUNT(*) FROM {table_name} WHERE "{revision_column}" = 0;')
                zero_count = cursor.fetchone()[0]
                
                if zero_count > 0:
                    db_has_zero_revisions = True
                    db_zero_count += zero_count
                    db_tables_with_zero.append({
                        'table': table_name,
                        'revision_column': revision_column,
                        'zero_count': zero_count
                    })
                    total_stats['tables_with_zero_revisions'] += 1
            
            total_stats['databases_processed'] += 1
            
            if db_has_zero_revisions:
                total_stats['databases_with_zero_revisions'] += 1
                total_stats['total_zero_revision_rows'] += db_zero_count
                
                databases_with_changes.append({
                    'path': db_file,
                    'zero_count': db_zero_count,
                    'tables': db_tables_with_zero
                })
            
            conn.close()
            
        except sqlite3.Error as e:
            print(f"Error processing {db_file}: {e}")
        except Exception as e:
            print(f"Unexpected error processing {db_file}: {e}")
    
    # Print detailed results
    if databases_with_changes:
        print("DATABASES WITH REVISION #0 ENTRIES:")
        print("-" * 60)
        
        for db_info in databases_with_changes:
            print(f"\n📁 {db_info['path']}")
            print(f"   Total revision #0 rows: {db_info['zero_count']}")
            
            for table_info in db_info['tables']:
                print(f"   📊 Table '{table_info['table']}': {table_info['zero_count']} rows")
                print(f"      Column: '{table_info['revision_column']}'")
    else:
        print("✅ No revision #0 entries found in any database!")
    
    # Print summary
    print("\n" + "=" * 60)
    print("SUMMARY")
    print(f"📊 Databases analyzed: {total_stats['databases_processed']}")
    print(f"📊 Databases with revision #0: {total_stats['databases_with_zero_revisions']}")
    print(f"📊 Tables with revision #0: {total_stats['tables_with_zero_revisions']}")
    print(f"📊 Total revision #0 rows: {total_stats['total_zero_revision_rows']}")
    
    if total_stats['total_zero_revision_rows'] > 0:
        print(f"\n🔄 Running the update script will change {total_stats['total_zero_revision_rows']} rows from revision #0 to revision #1")
        print("\nTo see exactly what would be changed, run:")
        print("   python3 update_revision_zero_to_one.py --dry-run")
        print("\nTo actually make the changes, run:")
        print("   python3 update_revision_zero_to_one.py")
    else:
        print("\n✅ No updates needed!")

if __name__ == "__main__":
    analyze_revision_zero_entries()
