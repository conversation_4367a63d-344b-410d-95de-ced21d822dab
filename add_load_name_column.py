#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to recursively add "Load Name" column to all tables in SQLite database files.

This script will:
1. Recursively find all .db files in the current directory and subdirectories
2. For each database file, get all table names
3. For each table, check if "Load Name" column exists
4. If not, add the "Load Name" column as TEXT type
5. Provide detailed logging of all operations
"""

import os
import sqlite3
import sys
from pathlib import Path


def get_all_db_files(root_dir):
    """Recursively find all .db files in the directory tree, excluding backup directories."""
    db_files = []
    for root, dirs, files in os.walk(root_dir):
        # Skip backup directories
        dirs[:] = [d for d in dirs if not d.startswith('backup_')]

        for file in files:
            if file.endswith('.db'):
                db_files.append(os.path.join(root, file))
    return sorted(db_files)


def get_table_names(db_path):
    """Get all table names from a SQLite database."""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]
        conn.close()
        return tables
    except Exception as e:
        print(f"Error getting tables from {db_path}: {e}")
        return []


def column_exists(db_path, table_name, column_name):
    """Check if a column exists in a table."""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = [row[1] for row in cursor.fetchall()]
        conn.close()
        return column_name in columns
    except Exception as e:
        print(f"Error checking column in {db_path}.{table_name}: {e}")
        return False


def add_load_name_column(db_path, table_name):
    """Add 'Load Name' column to a table if it doesn't exist."""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Add the column
        cursor.execute(f'ALTER TABLE "{table_name}" ADD COLUMN "Load Name" TEXT')
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        print(f"Error adding column to {db_path}.{table_name}: {e}")
        return False


def process_database(db_path):
    """Process a single database file."""
    print(f"\nProcessing database: {db_path}")
    
    tables = get_table_names(db_path)
    if not tables:
        print(f"  No tables found or error accessing database")
        return
    
    print(f"  Found {len(tables)} table(s): {', '.join(tables)}")
    
    for table in tables:
        if column_exists(db_path, table, "Load Name"):
            print(f"    Table '{table}': 'Load Name' column already exists - skipping")
        else:
            print(f"    Table '{table}': Adding 'Load Name' column...", end="")
            if add_load_name_column(db_path, table):
                print(" SUCCESS")
            else:
                print(" FAILED")


def main():
    """Main function to process all databases."""
    print("SQLite Database 'Load Name' Column Addition Script")
    print("=" * 50)
    
    # Get the current directory
    root_dir = os.getcwd()
    print(f"Scanning directory: {root_dir}")
    
    # Find all .db files
    db_files = get_all_db_files(root_dir)
    
    if not db_files:
        print("No .db files found in the directory tree.")
        return
    
    print(f"\nFound {len(db_files)} database file(s):")
    for db_file in db_files:
        print(f"  {db_file}")
    
    # Ask for confirmation
    response = input(f"\nProceed with adding 'Load Name' column to all tables? (y/N): ")
    if response.lower() not in ['y', 'yes']:
        print("Operation cancelled.")
        return
    
    # Process each database
    success_count = 0
    total_tables = 0
    
    for db_file in db_files:
        try:
            tables_before = len(get_table_names(db_file))
            process_database(db_file)
            tables_after = len(get_table_names(db_file))
            
            if tables_before == tables_after and tables_before > 0:
                success_count += 1
                total_tables += tables_before
        except Exception as e:
            print(f"Error processing {db_file}: {e}")
    
    print(f"\n" + "=" * 50)
    print(f"Processing complete!")
    print(f"Successfully processed {success_count} database(s)")
    print(f"Total tables processed: {total_tables}")
    print("\nNote: Tables that already had 'Load Name' column were skipped.")


if __name__ == "__main__":
    main()
